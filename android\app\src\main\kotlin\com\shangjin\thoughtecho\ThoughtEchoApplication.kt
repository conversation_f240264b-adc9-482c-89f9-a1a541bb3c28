package com.shangjin.thoughtecho

import android.app.Application
import android.os.Build
import android.util.Log
import androidx.multidex.MultiDexApplication

class ThoughtEchoApplication : MultiDexApplication() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 32位设备兼容性初始化
        if (!is64BitDevice()) {
            Log.i("ThoughtEchoApp", "初始化32位设备兼容性配置")
            initFor32BitDevice()
        } else {
            Log.i("ThoughtEchoApp", "检测到64位设备，使用标准配置")
        }
        
        // 初始化MMKV
        try {
            com.tencent.mmkv.MMKV.initialize(this)
            Log.i("ThoughtEchoApp", "MMKV初始化成功")
        } catch (e: Exception) {
            Log.e("ThoughtEchoApp", "MMKV初始化失败", e)
        }
    }
    
    /**
     * 32位设备特殊初始化配置
     */
    private fun initFor32BitDevice() {
        try {
            // 设置系统属性以优化32位设备性能
            System.setProperty("flutter.embedding.android.SurfaceProducerTextureRegistry.enable_surface_producer_texture_registry", "false")
            System.setProperty("flutter.embedding.android.SurfaceProducerTextureRegistry.enable_impeller", "false")
            
            // 禁用硬件加速相关的系统属性
            System.setProperty("ro.config.disable_hw_accel", "true")
            
            Log.i("ThoughtEchoApp", "32位设备系统属性配置完成")
        } catch (e: Exception) {
            Log.w("ThoughtEchoApp", "32位设备系统属性配置失败: ${e.message}")
        }
    }
    
    /**
     * 检查设备是否为64位架构
     */
    private fun is64BitDevice(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Build.SUPPORTED_64_BIT_ABIS.isNotEmpty()
            } else {
                val arch = System.getProperty("os.arch")?.lowercase() ?: ""
                arch.contains("64") || arch == "aarch64" || arch == "x86_64" || arch == "mips64"
            }
        } catch (e: Exception) {
            Log.e("ThoughtEchoApp", "检测设备架构时出错", e)
            false // 保守起见，当作32位处理
        }
    }
}
