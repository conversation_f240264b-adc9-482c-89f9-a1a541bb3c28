package com.shangjin.thoughtecho

import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {

    private val DEVICE_INFO_CHANNEL = "com.shangjin.thoughtecho/device_info"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        flutterEngine.plugins.add(MemoryInfoPlugin())
        flutterEngine.plugins.add(StreamFileSelector())

        // 设置设备信息通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, DEVICE_INFO_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "is64BitDevice" -> {
                    result.success(is64BitDevice())
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // 在super.onCreate()之前进行32位设备检测和配置
        if (!is64BitDevice()) {
            Log.i("MainActivity", "检测到32位设备，应用兼容性配置")
            try {
                // 确保在32位设备上禁用硬件加速
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                    0
                )
            } catch (e: Exception) {
                Log.w("MainActivity", "无法在onCreate前设置窗口标志: ${e.message}")
            }
        } else {
            Log.i("MainActivity", "检测到64位设备，保持默认配置")
        }

        super.onCreate(savedInstanceState)

        // 在super.onCreate()之后进行额外的32位设备优化
        if (!is64BitDevice()) {
            try {
                // 确保硬件加速被完全禁用
                window.clearFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
                Log.i("MainActivity", "32位设备硬件加速已禁用")
            } catch (e: Exception) {
                Log.w("MainActivity", "无法清除硬件加速标志: ${e.message}")
            }
        }
    }

    /**
     * 检查设备是否为64位架构
     * 返回true表示64位设备，false表示32位设备
     * Note: This function is currently unused.
     */
    private fun is64BitDevice(): Boolean {
        return try {
            // Android API 21+ (Lollipop及以上)可以通过Build类直接检测
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Build.SUPPORTED_64_BIT_ABIS.isNotEmpty()
            } else {
                // 对于更老的设备，通过CPU架构字符串判断
                // 注意：这种方法可能不完全可靠，但作为一种兼容性尝试
                val arch = System.getProperty("os.arch")?.lowercase() ?: ""
                // Log.d("MainActivity", "Device architecture string: $arch") // Log the architecture string for debugging
                arch.contains("64") || arch == "aarch64" || arch == "x86_64" || arch == "mips64"
            }
        } catch (e: Exception) {
            // 如果发生异常，保守起见返回false (当作32位处理)
            // Log.e("MainActivity", "Error checking device architecture", e)
            false
        }
    }
}